
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronLeft, ChevronRight, Loader2, Search } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import BrandSelector from '@/components/BrandSelector';
import SectionHeader from '@/components/SectionHeader';
import AnimatedCard from '@/components/AnimatedCard';

/**
 * @typedef {Object} SupportedModel
 * @property {string} id
 * @property {string} brand
 * @property {string} model
 * @property {string|null} security
 * @property {string|null} carrier
 * @property {string|null} operation
 */

/**
 * @typedef {Object} SupportedModelsProps
 * @property {'software'|'hardware'} [theme]
 */

const SupportedModels = ({ theme = 'software' }) => {
  const { toast } = useToast();
  const [brands, setBrands] = useState([]);
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);
  const [models, setModels] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const modelsPerPage = 6;

  // Function to get theme-aware colors
  const getThemeColors = () => {
    if (theme === 'hardware') {
      return {
        primary: 'text-pegasus-blue-600 dark:text-pegasus-blue-400',
        border: 'border-pegasus-blue-200 dark:border-pegasus-blue-900/30',
        hover: 'hover:bg-pegasus-blue-600/10 dark:hover:bg-pegasus-blue-900/10',
        borderHover: 'border-pegasus-blue-100 dark:border-pegasus-blue-900/20',
        focus: 'focus:border-pegasus-blue-600 focus:ring-pegasus-blue-600',
        background: 'bg-pegasus-blue-600/5 dark:from-gray-800 dark:to-gray-900',
        sectionBg: 'bg-pegasus-blue-600/3 dark:bg-gray-900'
      };
    }
    return {
      primary: 'text-orange-600 dark:text-orange-400',
      border: 'border-orange-200 dark:border-orange-900/30',
      hover: 'hover:bg-orange-600/10 dark:hover:bg-orange-900/10',
      borderHover: 'border-orange-100 dark:border-orange-900/20',
      focus: 'focus:border-pegasus-orange focus:ring-pegasus-orange',
      background: 'bg-orange-600/5 dark:from-gray-800 dark:to-gray-900',
      sectionBg: 'bg-orange-600/3 dark:bg-gray-900'
    };
  };

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const {
          data,
          error
        } = await supabase.from('supported_models').select('brand').order('brand');
        if (error) throw error;

        // Extract unique brands
        const uniqueBrands = Array.from(new Set(data.map(item => item.brand)));
        setBrands(uniqueBrands);

        // Select first brand by default if there are brands
        if (uniqueBrands.length > 0 && !selectedBrand) {
          setSelectedBrand(uniqueBrands[0]);
        }
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching brands:", error);
        toast({
          title: "Error",
          description: "Failed to load brands. Please try again later.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };
    fetchBrands();
  }, []);

  useEffect(() => {
    if (selectedBrand) {
      fetchModelsByBrand(selectedBrand);
    }
  }, [selectedBrand]);

  const fetchModelsByBrand = async (brand) => {
    setIsLoading(true);
    try {
      const {
        data,
        error
      } = await supabase.from('supported_models').select('*').eq('brand', brand).order('model');
      if (error) throw error;
      setModels(data);
      setCurrentPage(1); // Reset to first page when changing brands
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching models:", error);
      toast({
        title: "Error",
        description: "Failed to load models. Please try again later.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const handleBrandSelect = (brand) => {
    setSelectedBrand(brand);
    setSearchQuery(""); // Clear search when changing brands
  };

  const filteredModels = models.filter(model =>
    model.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (model.carrier && model.carrier.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (model.security && model.security.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (model.operation && model.operation.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredModels.length / modelsPerPage);
  const indexOfLastModel = currentPage * modelsPerPage;
  const indexOfFirstModel = indexOfLastModel - modelsPerPage;
  const currentModels = filteredModels.slice(indexOfFirstModel, indexOfLastModel);

  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const themeColors = getThemeColors();

  return (
    <div className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Header Section */}
      <div className="container mx-auto px-4 relative z-10 mb-16">
        <div className="text-center">
          <div className="mb-6">
            <span className={`bg-[#1a1a1a] border border-gray-700 px-4 py-1 rounded-full text-xs sm:text-sm font-medium ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}`}>
              Supported Models
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            Compatible <span className={theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}>Device Models</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Comprehensive support for major smartphone brands and models
          </p>
        </div>
      </div>

      {/* Brands Section */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col items-center mb-12">
          <h3 className="text-xl font-semibold mb-6 text-center text-white">Select a Brand</h3>

          <BrandSelector
            brands={brands}
            selectedBrand={selectedBrand}
            onBrandSelect={handleBrandSelect}
            isLoading={isLoading}
            className="opacity-0 animate-fade-in"
          />
        </div>
      </div>

      {/* Models Section */}
      {selectedBrand && (
        <div className="container mx-auto px-4 relative z-10">
          <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg shadow-xl overflow-hidden">
            <div className="flex flex-col md:flex-row justify-between items-center border-b border-gray-700/50 p-6">
              <h3 className="text-2xl font-bold text-white mb-4 md:mb-0">
                {selectedBrand} Models
              </h3>
              <div className="relative w-full md:w-64">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search models..."
                  className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-gray-500 focus:ring-gray-500"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="p-6">
              {isLoading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-10 bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : currentModels.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-700/50">
                        <TableHead className="w-[250px] text-gray-200 font-semibold">Model</TableHead>
                        <TableHead className="text-gray-200 font-semibold">Security</TableHead>
                        <TableHead className="text-gray-200 font-semibold">Carrier</TableHead>
                        <TableHead className="text-gray-200 font-semibold">Operation</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {currentModels.map((model, index) => (
                        <TableRow
                          key={model.id}
                          className={`border-gray-700/30 hover:bg-gray-800/50 transition-colors ${theme === 'hardware' ? 'hover:border-pegasus-blue-400/20' : 'hover:border-orange-400/20'}`}
                          style={{
                            opacity: 0,
                            animation: 'fade-in 0.3s ease-out forwards',
                            animationDelay: `${index * 0.05 + 0.1}s`
                          }}
                        >
                          <TableCell className="font-medium text-white">{model.model}</TableCell>
                          <TableCell className="text-gray-300">{model.security || "—"}</TableCell>
                          <TableCell className="text-gray-300">{model.carrier || "—"}</TableCell>
                          <TableCell className="text-gray-300">{model.operation || "—"}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-8">
                    <div className="text-sm text-gray-400">
                      Showing {filteredModels.length > 0 ? indexOfFirstModel + 1 : 0}-{Math.min(indexOfLastModel, filteredModels.length)} of {filteredModels.length} models
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors ${theme === 'hardware' ? 'hover:border-pegasus-blue-400' : 'hover:border-orange-400'}`}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <span className="text-sm text-gray-300">{currentPage} / {totalPages || 1}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages || totalPages === 0}
                        className={`bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors ${theme === 'hardware' ? 'hover:border-pegasus-blue-400' : 'hover:border-orange-400'}`}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  </div>
              ) : (
                <div className="text-center py-16 opacity-0 animate-fade-in" style={{animationFillMode: 'forwards'}}>
                  <p className="text-xl text-gray-400 mb-2">
                    {searchQuery ? "No models found matching your search" : `No models found for ${selectedBrand}`}
                  </p>
                  <p className="text-gray-500">
                    Try adjusting your search or selecting a different brand
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportedModels;
