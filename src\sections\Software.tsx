
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { ArrowRight, Download, CheckCircle2, Smartphone, Zap, RefreshCw } from "lucide-react";
import SectionHeader from '@/components/SectionHeader';
import AnimatedCard from '@/components/AnimatedCard';
import AnimatedCounter from '@/components/AnimatedCounter';
import SupportedModels from '@/sections/SupportedModels';
import Pricing from '@/sections/Pricing';
import ThemedSection from '@/components/layout/ThemedSection';
import ShinyText from '@/components/ui/shiny-text';
import ThemedHeader from '@/components/layout/ThemedHeader';
import StandardSection from '@/components/layout/StandardSection';
import { use3DEffect } from '@/hooks/use3DEffect';
import { CardContent } from '@/components/ui/card';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const Software = () => {
  const { toast: toastNotify } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const [homeImageUrl, setHomeImageUrl] = useState("/lovable-uploads/46319556-27d1-46f3-b365-81927d12674f.png");
  const [latestUpdate, setLatestUpdate] = useState<{
    varizon: string;
    name: string | null;
    link: string | null;
  } | null>(null);

  // Add 3D effect for the software image card
  const softwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });
  const [stats, setStats] = useState({
    totalModels: 0,
    downloadCount: 0,
    distributorsCount: 0
  });

  useEffect(() => {
    setIsVisible(true);

    // Fetch home image from Supabase storage
    const fetchHomeImage = async () => {
      try {
        // Get home image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHomeImageUrl(imageData.publicUrl);
        }
      } catch (error) {
        console.error('Error fetching home image:', error);
        toastNotify({
          title: "Image Loading Error",
          description: "Could not load the home image. Using fallback image instead.",
          variant: "destructive",
        });
      }
    };

    // Fetch latest software update
    const fetchLatestUpdate = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestUpdate(data[0]);
        }
      } catch (error) {
        console.error('Error fetching latest update:', error);
      }
    };

    // Fetch statistics
    const fetchStats = async () => {
      try {
        // Get total models count
        const { data: modelSettings, error: modelError } = await supabase
          .from('settings')
          .select('numeric_value')
          .eq('key', 'total_models')
          .single();

        if (modelError) console.error('Error fetching total models:', modelError);

        // Get download count
        const { data: updateData, error: updateError } = await supabase
          .from('update')
          .select('download_count')
          .order('release_at', { ascending: false })
          .limit(1);

        if (updateError) console.error('Error fetching download count:', updateError);

        setStats({
          totalModels: modelSettings?.numeric_value || 0,
          downloadCount: updateData?.[0]?.download_count || 0,
          distributorsCount: stats.distributorsCount
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchHomeImage();
    fetchLatestUpdate();
    fetchStats();
  }, []);

  const handleDownload = async () => {
    try {
      if (latestUpdate?.link) {
        // Call the increment_counter function
        const { data, error: counterError } = await supabase.rpc('increment_counter');

        if (counterError) {
          console.error('Error incrementing download counter:', counterError);
          toast.error('Failed to process download request');
        } else {
          console.log('Download count increased to:', data);

          // Open the download link
          window.location.href = latestUpdate.link;
          toast.success('Download started!');
        }
      } else {
        toast.info("Download link is not available at the moment. Please try again later.");
      }
    } catch (error) {
      console.error('Error during download:', error);
      // Still provide download link even if counting fails
      if (latestUpdate?.link) {
        window.location.href = latestUpdate.link;
      }
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-[#111111] text-gray-300 min-h-screen relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

        {/* Animated particles - Software themed */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-orange-400/10"
            style={{
              width: Math.random() * 60 + 30,
              height: Math.random() * 60 + 30,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -120, 0],
              x: [0, Math.random() * 60 - 30, 0],
              opacity: [0, 0.6, 0],
            }}
            transition={{
              duration: Math.random() * 12 + 8,
              repeat: Infinity,
              delay: Math.random() * 6,
            }}
          />
        ))}

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-900/20 via-transparent to-orange-800/10"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-[#111111] via-transparent to-transparent"></div>

        <main className="flex-grow flex flex-col items-center justify-center text-center px-4 pt-8 pb-16 relative z-10">

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <ShinyText
              text="Professional Software Solutions"
              className="bg-[#1a1a1a] border border-gray-700 text-orange-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer hover:border-orange-400/50 transition-colors"
            />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl sm:text-5xl lg:text-[64px] font-semibold text-white leading-tight max-w-4xl mb-4"
          >
            Pegasus Tool for<br />
            <span className="text-orange-400">Software</span> Solutions
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto mb-8"
          >
            Professional software solution for device unlocking, firmware flashing, and repair operations.
            Supporting 200+ smartphone models with enterprise-grade reliability.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col md:flex-row items-center justify-center gap-6 mb-12"
          >
            <motion.button
              className="bg-orange-500 text-white hover:bg-orange-600 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={handleDownload}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Download className="mr-3 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="relative">
                Get Pegasus Tool {latestUpdate && `v${latestUpdate.varizon}`}
              </span>
            </motion.button>

            <motion.button
              className="bg-transparent border-2 border-orange-400 text-orange-400 hover:bg-orange-400 hover:text-white px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={() => document.getElementById('software-supported-models')?.scrollIntoView({ behavior: 'smooth' })}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Smartphone className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
              <span className="relative">Explore Features</span>
            </motion.button>
          </motion.div>
          {/* Software Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
          >
            <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-orange-400/50 transition-all duration-300 hover:-translate-y-2">
              <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-orange-900/30 to-orange-800/20 rounded-full mb-4 shadow-md mx-auto">
                <CheckCircle2 className="h-6 w-6 text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-white">Professional Interface</h3>
              <p className="text-gray-400 text-sm">Enterprise-grade interface for technicians</p>
            </div>

            <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-orange-400/50 transition-all duration-300 hover:-translate-y-2">
              <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-orange-900/30 to-orange-800/20 rounded-full mb-4 shadow-md mx-auto">
                <Smartphone className="h-6 w-6 text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-white">Universal Compatibility</h3>
              <p className="text-gray-400 text-sm">Support for 200+ device models</p>
            </div>

            <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-orange-400/50 transition-all duration-300 hover:-translate-y-2">
              <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-orange-900/30 to-orange-800/20 rounded-full mb-4 shadow-md mx-auto">
                <RefreshCw className="h-6 w-6 text-orange-400" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-white">Continuous Updates</h3>
              <p className="text-gray-400 text-sm">Regular updates with new features</p>
            </div>
          </motion.div>

        </main>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
        <div className="absolute inset-0 z-1 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-orange-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
                Software Features
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
              Discover the power of <span className="text-orange-400">Pegasus Tool</span>
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
              Professional software solution with advanced features for device unlocking and repair
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            {[
              {
                title: 'Professional Interface',
                icon: CheckCircle2,
                description: 'Enterprise-grade interface designed for technicians and repair professionals with streamlined workflows and advanced diagnostics.'
              },
              {
                title: 'Universal Compatibility',
                icon: Smartphone,
                description: 'Comprehensive support for 200+ device models including Xiaomi, Samsung, Oppo, Vivo, Realme, and emerging brands.'
              },
              {
                title: 'Continuous Innovation',
                icon: RefreshCw,
                description: 'Regular updates with new device support, security patches, and cutting-edge features to stay ahead of industry demands.'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 text-center backdrop-blur-lg hover:border-orange-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-900/30 to-orange-800/20 rounded-full mb-6 shadow-md mx-auto">
                    <feature.icon className="h-8 w-8 text-orange-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-white">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Supported Models Section */}
      <ThemedSection
        theme="software"
        id="software-supported-models"
        variant="primary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Supported Device Models"
          subtitle="Complete software compatibility for unlocking and repair solutions"
          highlightWord="Device"
          size="normal"
        />
        <SupportedModels theme="software" />
      </ThemedSection>

      {/* Pricing Section */}
      <ThemedSection
        theme="software"
        id="software-pricing"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Software Solutions Pricing"
          subtitle="Complete software tools for device unlocking and repair with flexible pricing options"
          highlightWord="Pricing"
          size="normal"
        />
        <Pricing theme="software" />
      </ThemedSection>
    </div>
  );
};

export default Software;



