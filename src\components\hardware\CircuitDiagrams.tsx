
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { use3DEffect } from '@/hooks/use3DEffect';
import { cn } from '@/lib/utils';
import ThemedHeader from '@/components/layout/ThemedHeader';
import Text3D from '@/components/3D/Text3D';
import { CircuitBoard, Microchip, Cpu } from 'lucide-react';

const CircuitDiagrams = () => {
  const diagramRef = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: true
  });

  const [activeCircuit, setActiveCircuit] = useState<'main' | 'power' | 'io'>('main');

  const circuitImages = {
    main: '/images/main-circuit.png',
    power: '/images/power-circuit.png',
    io: '/images/io-circuit.png'
  };

  return (
    <section id="circuit-diagrams" className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="mb-6">
            <span className="bg-[#1a1a1a] border border-gray-700 text-pegasus-blue-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
              Technical Schematics
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            Professional <span className="text-pegasus-blue-400">Circuit Diagrams</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Professional-grade circuit diagrams and component layouts for precision hardware repair
          </p>
        </motion.div>

        <div className="flex flex-col lg:flex-row items-center gap-10 mt-16">
          {/* Right side - Interactive Circuit */}
          <motion.div
            className="lg:w-1/2 order-1 lg:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="relative">
              <Card
                ref={diagramRef}
                className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2 shadow-xl"
              >
                <div className="relative">
                  <img
                    src={circuitImages[activeCircuit]}
                    alt="Circuit Diagram"
                    className="w-full rounded-lg"
                  />
                </div>

                <div className="flex justify-center mt-6 gap-3">
                  <button
                    onClick={() => setActiveCircuit('main')}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      activeCircuit === 'main'
                        ? "bg-pegasus-blue-500 text-white shadow-lg"
                        : "bg-gray-800 text-gray-300 hover:bg-pegasus-blue-500/20 hover:text-pegasus-blue-400"
                    )}
                  >
                    Motherboard
                  </button>
                  <button
                    onClick={() => setActiveCircuit('power')}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      activeCircuit === 'power'
                        ? "bg-pegasus-blue-500 text-white shadow-lg"
                        : "bg-gray-800 text-gray-300 hover:bg-pegasus-blue-500/20 hover:text-pegasus-blue-400"
                    )}
                  >
                    Power Unit
                  </button>
                  <button
                    onClick={() => setActiveCircuit('io')}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      activeCircuit === 'io'
                        ? "bg-pegasus-blue-500 text-white shadow-lg"
                        : "bg-gray-800 text-gray-300 hover:bg-pegasus-blue-500/20 hover:text-pegasus-blue-400"
                    )}
                  >
                    Interfaces
                  </button>
                </div>
              </Card>
            </div>
          </motion.div>

          {/* Left side - Explanation */}
          <motion.div
            className="lg:w-1/2 order-2 lg:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <Text3D
              as="h2"
              size="3xl"
              color="text-pegasus-blue-600"
              className="mb-6"
            >
              Advanced Hardware Documentation
            </Text3D>

            <div className="space-y-6 text-gray-700 dark:text-gray-300">
              <p className="text-lg">
                Access comprehensive technical documentation with high-resolution schematics, component specifications, and repair procedures. Our interactive diagrams feature precise measurements, signal flow analysis, and component identification for professional-grade repairs.
              </p>

              <div className="space-y-4 mt-6">
                <div className="flex items-start gap-4">
                  <div className="bg-pegasus-blue-100 dark:bg-pegasus-blue-900/30 p-3 rounded-full">
                    <CircuitBoard className="h-6 w-6 text-pegasus-blue-600 dark:text-pegasus-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-pegasus-blue-600 dark:text-pegasus-blue-500">Motherboard Architecture</h3>
                    <p>Complete mainboard layout with CPU, memory controllers, and critical system components. Includes detailed pin configurations and signal routing paths.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-pegasus-blue-100 dark:bg-pegasus-blue-900/30 p-3 rounded-full">
                    <Microchip className="h-6 w-6 text-pegasus-blue-600 dark:text-pegasus-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-pegasus-blue-600 dark:text-pegasus-blue-500">Power Management Unit</h3>
                    <p>Advanced power distribution schematics featuring PMIC layouts, voltage regulation circuits, and battery management systems with fault protection.</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-pegasus-blue-100 dark:bg-pegasus-blue-900/30 p-3 rounded-full">
                    <Cpu className="h-6 w-6 text-pegasus-blue-600 dark:text-pegasus-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-pegasus-blue-600 dark:text-pegasus-blue-500">Interface Controllers</h3>
                    <p>Comprehensive I/O mapping including USB controllers, display interfaces, audio codecs, and wireless communication modules with pinout specifications.</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-pegasus-blue-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CircuitDiagrams;
