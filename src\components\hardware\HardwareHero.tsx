
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Download, ArrowRight, CircuitBoard, Cpu, HardDrive, Zap } from 'lucide-react';
import { use3DEffect } from '@/hooks/use3DEffect';

// ShinyText component
const ShinyText: React.FC<{ text: string; className?: string }> = ({ text, className = "" }) => (
  <span className={`relative overflow-hidden inline-block ${className}`}>
    {text}
    <span style={{
      position: 'absolute',
      inset: 0,
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
      animation: 'shine 2s infinite linear',
      opacity: 0.5,
      pointerEvents: 'none'
    }}></span>
    <style>{`
      @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
    `}</style>
  </span>
);
const HardwareHero = () => {
  const hardwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });

  const [hardwareImageUrl, setHardwareImageUrl] = useState("/lovable-uploads/hardware-diagram.png");
  const [latestDiagram, setLatestDiagram] = useState<{
    version: string,
    name: string | null,
    link: string | null
  } | null>(null);

  useEffect(() => {
    // Fetch hardware diagram image and link
    const fetchHardwareData = async () => {
      try {
        // Get hardware image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHardwareImageUrl(imageData.publicUrl);
        }

        // For now, let's use a mock diagram data since the hardware_diagrams table doesn't exist
        setLatestDiagram({
          version: "1.0",
          name: "Circuit Diagram",
          link: "/lovable-uploads/hardware-diagram.png"
        });

        // When the hardware_diagrams table exists, you can uncomment this code:
        /*
        const { data, error } = await supabase
          .from('hardware_diagrams')
          .select('version, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestDiagram(data[0]);
        }
        */
      } catch (error) {
        console.error('Error fetching hardware data:', error);
      }
    };

    fetchHardwareData();
  }, []);

  const handleDownload = () => {
    if (latestDiagram?.link) {
      window.location.href = latestDiagram.link;
      toast.success('Download started!');
    } else {
      toast.info("Download link is not available at the moment. Please try again later.");
    }
  };

  return (
    <div className="pt-[100px] relative bg-[#111111] text-gray-300 min-h-screen flex flex-col overflow-x-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 pt-8 pb-16 relative z-10">

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-6"
        >
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-4xl sm:text-5xl lg:text-[64px] font-semibold text-white leading-tight max-w-4xl mb-4"
        >
          Pegasus Tool for<br />
          <span className="text-pegasus-blue-400">Hardware</span> Solutions
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto mb-8"
        >
          Professional hardware documentation and circuit diagrams for mobile device repair with detailed schematics, component layouts, and technical resources for technicians worldwide.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-4 w-full max-w-md mx-auto mb-8"
        >
          <motion.button
            onClick={handleDownload}
            className="w-full sm:w-auto bg-pegasus-blue-500 text-white px-6 py-3 rounded-md text-sm font-semibold hover:bg-pegasus-blue-500/80 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md flex-shrink-0"
            whileHover={{ scale: 1.03, y: -1 }}
            whileTap={{ scale: 0.97 }}
            transition={{ type: "spring", stiffness: 400, damping: 15 }}
          >
            Access Hardware Resources {latestDiagram && `v${latestDiagram.version}`}
          </motion.button>

          <motion.button
            onClick={() => document.getElementById('circuit-diagrams')?.scrollIntoView({ behavior: 'smooth' })}
            className="w-full sm:w-auto bg-pegasus-blue-500 text-white px-6 py-3 rounded-md text-sm font-semibold hover:bg-pegasus-blue-500/80 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md flex-shrink-0"
            whileHover={{ scale: 1.03, y: -1 }}
            whileTap={{ scale: 0.97 }}
            transition={{ type: "spring", stiffness: 400, damping: 15 }}
          >
            Browse Components
          </motion.button>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-wrap items-center justify-center gap-x-6 gap-y-2 text-gray-400 text-sm"
        >
          <span className="flex items-center whitespace-nowrap">✓ Circuit Diagrams</span>
          <span className="flex items-center whitespace-nowrap">✓ Component Analysis</span>
          <span className="flex items-center whitespace-nowrap">✓ Repair Guides</span>
          <span className="flex items-center whitespace-nowrap">✓ Technical Documentation</span>
        </motion.div>

      </main>
    </div>
  );
};

export default HardwareHero;
