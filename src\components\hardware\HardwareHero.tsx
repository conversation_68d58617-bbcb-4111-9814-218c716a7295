
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Download, ArrowRight, CircuitBoard, Cpu, HardDrive, Zap } from 'lucide-react';
import { use3DEffect } from '@/hooks/use3DEffect';
import ShinyText from '@/components/ui/shiny-text';

const HardwareHero = () => {
  const hardwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });

  const [hardwareImageUrl, setHardwareImageUrl] = useState("/lovable-uploads/hardware-diagram.png");
  const [latestDiagram, setLatestDiagram] = useState<{
    version: string,
    name: string | null,
    link: string | null
  } | null>(null);

  useEffect(() => {
    // Fetch hardware diagram image and link
    const fetchHardwareData = async () => {
      try {
        // Get hardware image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHardwareImageUrl(imageData.publicUrl);
        }

        // For now, let's use a mock diagram data since the hardware_diagrams table doesn't exist
        setLatestDiagram({
          version: "1.0",
          name: "Circuit Diagram",
          link: "/lovable-uploads/hardware-diagram.png"
        });

        // When the hardware_diagrams table exists, you can uncomment this code:
        /*
        const { data, error } = await supabase
          .from('hardware_diagrams')
          .select('version, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestDiagram(data[0]);
        }
        */
      } catch (error) {
        console.error('Error fetching hardware data:', error);
      }
    };

    fetchHardwareData();
  }, []);

  const handleDownload = () => {
    if (latestDiagram?.link) {
      window.location.href = latestDiagram.link;
      toast.success('Download started!');
    } else {
      toast.info("Download link is not available at the moment. Please try again later.");
    }
  };

  return (
    <section className="bg-[#111111] text-gray-300 min-h-screen relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

      {/* Animated particles - Hardware themed */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-pegasus-blue-400/10"
          style={{
            width: Math.random() * 60 + 30,
            height: Math.random() * 60 + 30,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -120, 0],
            x: [0, Math.random() * 60 - 30, 0],
            opacity: [0, 0.6, 0],
          }}
          transition={{
            duration: Math.random() * 12 + 8,
            repeat: Infinity,
            delay: Math.random() * 6,
          }}
        />
      ))}

      {/* Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-br from-pegasus-blue-900/20 via-transparent to-pegasus-blue-800/10"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-[#111111] via-transparent to-transparent"></div>

      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 pt-8 pb-16 relative z-10">

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-6"
        >
          <ShinyText
            text="Professional Hardware Solutions"
            className="bg-[#1a1a1a] border border-gray-700 text-pegasus-blue-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer hover:border-pegasus-blue-400/50 transition-colors"
          />
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-4xl sm:text-5xl lg:text-[64px] font-semibold text-white leading-tight max-w-4xl mb-4"
        >
          Pegasus Tool for<br />
          <span className="text-pegasus-blue-400">Hardware</span> Solutions
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto mb-8"
        >
          Professional hardware documentation and circuit diagrams for mobile device repair.
          Access detailed schematics, component layouts, and technical resources for precise diagnostics.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex flex-col md:flex-row items-center justify-center gap-6 mb-12"
        >
          <motion.button
            className="bg-pegasus-blue-500 text-white hover:bg-pegasus-blue-600 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
            onClick={handleDownload}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download className="mr-3 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
            <span className="relative">
              Access Hardware Resources {latestDiagram && `v${latestDiagram.version}`}
            </span>
          </motion.button>

          <motion.button
            className="bg-transparent border-2 border-pegasus-blue-400 text-pegasus-blue-400 hover:bg-pegasus-blue-400 hover:text-white px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
            onClick={() => document.getElementById('circuit-diagrams')?.scrollIntoView({ behavior: 'smooth' })}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <CircuitBoard className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
            <span className="relative">Browse Components</span>
          </motion.button>
        </motion.div>
        {/* Hardware Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
        >
          <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
            <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
              <CircuitBoard className="h-6 w-6 text-pegasus-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-white">Circuit Diagrams</h3>
            <p className="text-gray-400 text-sm">Detailed schematics and layouts</p>
          </div>

          <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
            <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
              <Cpu className="h-6 w-6 text-pegasus-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-white">Component Analysis</h3>
            <p className="text-gray-400 text-sm">Professional component identification</p>
          </div>

          <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-pegasus-blue-400/50 transition-all duration-300 hover:-translate-y-2">
            <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-pegasus-blue-900/30 to-pegasus-blue-800/20 rounded-full mb-4 shadow-md mx-auto">
              <HardDrive className="h-6 w-6 text-pegasus-blue-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-white">Repair Guides</h3>
            <p className="text-gray-400 text-sm">Step-by-step repair instructions</p>
          </div>
        </motion.div>

      </main>
    </section>
  );
};

export default HardwareHero;
