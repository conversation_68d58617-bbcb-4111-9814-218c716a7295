
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { use3DEffect } from '@/hooks/use3DEffect';

// ShinyText component
const ShinyText: React.FC<{ text: string; className?: string }> = ({ text, className = "" }) => (
  <span className={`relative overflow-hidden inline-block ${className}`}>
    {text}
    <span style={{
      position: 'absolute',
      inset: 0,
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
      animation: 'shine 2s infinite linear',
      opacity: 0.5,
      pointerEvents: 'none'
    }}></span>
    <style>{`
      @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
    `}</style>
  </span>
);
const HardwareHero = () => {
  const hardwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });

  const [hardwareImageUrl, setHardwareImageUrl] = useState("/lovable-uploads/hardware-diagram.png");
  const [latestDiagram, setLatestDiagram] = useState<{
    version: string,
    name: string | null,
    link: string | null
  } | null>(null);

  useEffect(() => {
    // Fetch hardware diagram image and link
    const fetchHardwareData = async () => {
      try {
        // Get hardware image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHardwareImageUrl(imageData.publicUrl);
        }

        // For now, let's use a mock diagram data since the hardware_diagrams table doesn't exist
        setLatestDiagram({
          version: "1.0",
          name: "Circuit Diagram",
          link: "/lovable-uploads/hardware-diagram.png"
        });

        // When the hardware_diagrams table exists, you can uncomment this code:
        /*
        const { data, error } = await supabase
          .from('hardware_diagrams')
          .select('version, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestDiagram(data[0]);
        }
        */
      } catch (error) {
        console.error('Error fetching hardware data:', error);
      }
    };

    fetchHardwareData();
  }, []);

  const handleDownload = () => {
    if (latestDiagram?.link) {
      window.location.href = latestDiagram.link;
      toast.success('Download started!');
    } else {
      toast.info("Download link is not available at the moment. Please try again later.");
    }
  };

  return (
    <div className="pt-[30px] relative bg-[#111111] text-gray-300 min-h-screen flex flex-col overflow-x-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh]">

          {/* Content Section */}
          <motion.div
            className="lg:w-1/2 text-center lg:text-left mb-10 lg:mb-0"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-6"
            >
              <ShinyText
                text="Professional Hardware Documentation"
                className="bg-[#1a1a1a] border border-gray-700 text-pegasus-blue-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer hover:border-pegasus-blue-400/50 transition-colors"
              />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-4xl sm:text-5xl lg:text-6xl font-semibold text-white leading-tight mb-6"
            >
              Pegasus Tool for<br />
              <span className="text-pegasus-blue-400">Hardware</span> Solutions
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-lg lg:text-xl text-gray-400 max-w-lg mb-8"
            >
              Professional hardware documentation and circuit diagrams for mobile device repair with detailed schematics, component layouts, and technical resources for technicians worldwide.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row items-center lg:items-start lg:justify-start justify-center gap-4 mb-8"
            >
              <motion.button
                onClick={handleDownload}
                className="bg-pegasus-blue-500 text-white px-6 py-3 rounded-md text-sm font-semibold hover:bg-pegasus-blue-500/80 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                whileHover={{ scale: 1.03, y: -1 }}
                whileTap={{ scale: 0.97 }}
                transition={{ type: "spring", stiffness: 400, damping: 15 }}
              >
                Access Hardware Resources {latestDiagram && `v${latestDiagram.version}`}
              </motion.button>

              <motion.button
                onClick={() => document.getElementById('circuit-diagrams')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-transparent border-2 border-pegasus-blue-400 text-pegasus-blue-400 hover:bg-pegasus-blue-400 hover:text-white px-6 py-3 rounded-md text-sm font-semibold transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                whileHover={{ scale: 1.03, y: -1 }}
                whileTap={{ scale: 0.97 }}
                transition={{ type: "spring", stiffness: 400, damping: 15 }}
              >
                Browse Components
              </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-wrap items-center justify-center lg:justify-start gap-x-6 gap-y-2 text-gray-400 text-sm"
            >
            </motion.div>
          </motion.div>

          {/* Image Section */}
          <motion.div
            className="lg:w-1/2 relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <div className="relative flex items-center justify-center">
              {/* Glowing background effect */}
              <motion.div
                className="bg-gradient-to-br from-pegasus-blue-500/20 via-pegasus-blue-400/15 to-pegasus-blue-700/10 rounded-full h-80 w-80 lg:h-[420px] lg:w-[420px] mx-auto absolute blur-3xl"
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />

              <motion.div
                ref={hardwareImageRef}
                className="relative z-10 max-w-full lg:max-w-md mx-auto bg-[#1a1a1a]/40 backdrop-blur-sm rounded-2xl p-6 border border-pegasus-blue-500/20 shadow-2xl"
                animate={{
                  y: [0, -12, 0],
                }}
                transition={{
                  duration: 7,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                <img
                  src={hardwareImageUrl}
                  alt="Professional Hardware Circuit Diagrams and Component Layouts"
                  className="w-full h-full object-contain rounded-lg"
                  style={{
                    filter: 'drop-shadow(0 15px 25px rgba(0, 0, 0, 0.6)) brightness(1.1) contrast(1.05)'
                  }}
                  onError={(e) => {
                    const target = e.target;
                    if (target && target instanceof HTMLImageElement) {
                      target.src = "/lovable-uploads/hardware-diagram.png";
                    }
                  }}
                />

                {/* Animated circuit points */}
                <motion.div
                  className="absolute top-1/4 left-1/4 w-3 h-3 bg-pegasus-blue-400 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.7, 1, 0.7],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />
                <motion.div
                  className="absolute top-1/2 right-1/3 w-2 h-2 bg-pegasus-blue-300 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.6, 1, 0.6],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 0.5
                  }}
                />
                <motion.div
                  className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-pegasus-blue-200 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 1
                  }}
                />
              </motion.div>
            </div>
          </motion.div>

        </div>
      </div>
    </div>
  );
};

export default HardwareHero;
